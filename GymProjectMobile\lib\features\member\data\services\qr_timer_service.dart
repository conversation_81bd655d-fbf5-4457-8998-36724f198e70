/// QR Timer Service - GymKod Pro Mobile
///
/// Bu service Angular frontend'deki QR timer logic'inden uyarlanmıştır.
/// Referans: GymProjectFrontend/src/app/components/my-qr/my-qr.component.ts
library;

import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/core.dart';
import '../../domain/models/member_models.dart';

/// QR Timer Service
/// Angular frontend'deki timer logic'ini Flutter'a uyarlar
class QRTimerService {
  Timer? _timer;
  final StateNotifierProvider<QRTimerNotifier, QRTimerState> _timerProvider;
  Function()? _onExpiredCallback; // QR kod süresi bitince çağrılacak callback

  QRTimerService(this._timerProvider);

  /// Callback'i ayarla (QR kod süresi bitince çağrılacak)
  void setOnExpiredCallback(Function() callback) {
    _onExpiredCallback = callback;
  }

  /// Timer'ı başlat (Angular: startQRCodeTimer)
  /// QR kod 5 dakika geçerli (Backend pattern)
  void startTimer(Ref ref) {
    try {
      LoggingService.stateLog('QRTimer', 'Starting QR timer (5 minutes)');

      // Mevcut timer'ı durdur
      stopTimer();

      // QR kod 5 dakika geçerli (Backend'deki QR encryption pattern)
      const totalDuration = Duration(minutes: 5);

      // Timer state'ini başlat
      ref.read(_timerProvider.notifier).start(totalDuration);

      // Her saniye güncelle (Angular frontend pattern)
      _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
        final currentState = ref.read(_timerProvider);
        final elapsed = totalDuration - currentState.remainingTime + const Duration(seconds: 1);
        final remaining = totalDuration - elapsed;

        if (remaining.isNegative || remaining.inSeconds <= 0) {
          // Süre doldu
          LoggingService.stateLog('QRTimer', 'QR timer expired - triggering auto refresh');
          ref.read(_timerProvider.notifier).setExpired();
          stopTimer();

          // Otomatik yenileme callback'ini çağır (Angular pattern)
          if (_onExpiredCallback != null) {
            _onExpiredCallback!();
          }
        } else {
          // Kalan süreyi güncelle
          ref.read(_timerProvider.notifier).updateTime(remaining, totalDuration);
        }
      });

      LoggingService.stateLog('QRTimer', 'QR timer started successfully',
        state: 'Duration: 5:00');

    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'QRTimerService startTimer');
    }
  }

  /// Timer'ı durdur
  void stopTimer() {
    try {
      _timer?.cancel();
      _timer = null;
      LoggingService.stateLog('QRTimer', 'Timer stopped');
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'QRTimerService stopTimer');
    }
  }

  /// Timer'ı sıfırla
  void resetTimer(Ref ref) {
    try {
      stopTimer();
      ref.read(_timerProvider.notifier).reset();
      LoggingService.stateLog('QRTimer', 'Timer reset');
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'QRTimerService resetTimer');
    }
  }

  /// Service'i temizle
  void dispose() {
    stopTimer();
  }
}

/// QR Timer State Notifier
/// Angular frontend'deki timer state management'ını Flutter'a uyarlar
class QRTimerNotifier extends StateNotifier<QRTimerState> {
  QRTimerNotifier() : super(QRTimerState.initial());

  /// Timer'ı başlat
  void start(Duration totalDuration) {
    state = state.copyWith(
      remainingTime: totalDuration,
      totalTime: totalDuration,
      isActive: true,
      isExpired: false,
      progress: 1.0,
    );
  }

  /// Kalan süreyi güncelle
  void updateTime(Duration remainingTime, Duration totalDuration) {
    final progress = remainingTime.inSeconds / totalDuration.inSeconds;

    state = state.copyWith(
      remainingTime: remainingTime,
      progress: progress.clamp(0.0, 1.0),
    );
  }

  /// Timer'ı süresi dolmuş olarak işaretle
  void setExpired() {
    state = state.copyWith(
      remainingTime: Duration.zero,
      isActive: false,
      isExpired: true,
      progress: 0.0,
    );
  }

  /// Timer'ı sıfırla
  void reset() {
    state = QRTimerState.initial();
  }

  /// Timer'ı durdur
  void stop() {
    state = state.copyWith(
      isActive: false,
    );
  }
}

/// QR Timer Provider
final qrTimerProvider = StateNotifierProvider<QRTimerNotifier, QRTimerState>((ref) {
  return QRTimerNotifier();
});

/// QR Timer Service Provider
final qrTimerServiceProvider = Provider<QRTimerService>((ref) {
  return QRTimerService(qrTimerProvider);
});

/// QR Timer Getters
final qrTimerRemainingTimeProvider = Provider<String>((ref) {
  final timerState = ref.watch(qrTimerProvider);
  return timerState.formattedTime;
});

final qrTimerProgressProvider = Provider<double>((ref) {
  final timerState = ref.watch(qrTimerProvider);
  return timerState.progress;
});

final qrTimerIsActiveProvider = Provider<bool>((ref) {
  final timerState = ref.watch(qrTimerProvider);
  return timerState.isActive;
});

final qrTimerIsExpiredProvider = Provider<bool>((ref) {
  final timerState = ref.watch(qrTimerProvider);
  return timerState.isExpired;
});

final qrTimerIsWarningProvider = Provider<bool>((ref) {
  final timerState = ref.watch(qrTimerProvider);
  return timerState.remainingTime.inSeconds < 60; // Son 1 dakika warning
});
